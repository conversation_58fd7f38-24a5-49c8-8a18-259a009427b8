import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatStepperModule } from '@angular/material/stepper';
import { RouterLink } from '@angular/router';

interface CarouselSlide {
  title: string;
  subtitle: string;
  buttonText: string;
  buttonLink: string;
  image: string;
}

interface Feature {
  icon: string;
  title: string;
  description: string;
}

interface Advantage {
  icon: string;
  title: string;
  description: string;
}

interface Service {
  title: string;
  description: string;
  features: string[];
  icon: string;
  buttonLink: string;
}

interface InvestmentStep {
  label: string;
  description: string;
  features: string[];
}

interface Sector {
  title: string;
  description: string;
  investment: string;
  projects: string;
  opportunities: string[];
  image: string;
  buttonLink: string;
}

interface DashboardStat {
  label: string;
  value: string;
  change: string;
}

interface SupportOption {
  title: string;
  description: string;
  icon: string;
  buttonText: string;
  buttonLink: string;
}

@Component({
  selector: 'app-homepage',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatStepperModule
    // Remove RouterLink if not used in the template
  ],
  templateUrl: './homepage.component.html',
  styleUrls: ['./homepage.component.scss']
})
export class HomepageComponent implements OnInit {
  currentSlide = 0;
  mobileMenuOpen = false;
  isMobile = window.innerWidth <= 768;

  carouselSlides: CarouselSlide[] = [
    {
      title: 'Invest in Tripura with Confidence',
      subtitle: 'Single-window approvals, real-time tracking, and transparent governance.',
      buttonText: 'Start Your Application',
      buttonLink: '/auth/registration',
      image: 'assets/images/slide1.png'
    },
    {
      title: 'Explore Tripura’s Opportunities',
      subtitle: 'Discover diverse investment sectors in Northeast India.',
      buttonText: 'Explore Sectors',
      buttonLink: '/sectors',
      image: 'assets/images/slide2.png'
    },
    {
      title: 'Digital-First Investment',
      subtitle: 'Experience India’s most investor-friendly digital ecosystem.',
      buttonText: 'Learn More',
      buttonLink: '/dashboard',
      image: 'assets/images/slide3.png'
    }
  ];

  features: Feature[] = [
    {
      icon: 'view_module',
      title: 'Single-Window System',
      description: 'One platform for all investment approvals and clearances.'
    },
    {
      icon: 'cloud',
      title: 'Digital-First Process',
      description: 'Streamlined online applications with real-time tracking.'
    },
    {
      icon: 'support_agent',
      title: 'Dedicated Support',
      description: 'Personal investment facilitation and guidance.'
    },
    {
      icon: 'verified',
      title: 'Transparent Governance',
      description: 'Clear timelines and accountability at every step.'
    }
  ];

  advantages: Advantage[] = [
    {
      icon: 'timer',
      title: 'Reduced Approval Time',
      description: 'Approval times reduced by 60% with streamlined processes.'
    },
    {
      icon: 'contact_support',
      title: 'Single-Point Contact',
      description: 'Single-point contact for all clearances.'
    },
    {
      icon: 'track_changes',
      title: 'Real-Time Tracking',
      description: 'Monitor your application status in real-time.'
    },
    {
      icon: 'card_giftcard',
      title: 'Investment Incentives',
      description: 'Comprehensive incentives including subsidies and tax holidays.'
    }
  ];

  services: Service[] = [
    {
      title: 'Common Application Form',
      description: 'Unified digital application for all investment proposals.',
      icon: 'form',
      features: ['Single form submission', 'Document management', 'Status tracking'],
      buttonLink: '/services/caf'
    },
    {
      title: 'GIS Land Bank Viewer',
      description: 'Interactive map-based land selection and availability.',
      icon: 'map',
      features: ['Real-time availability', 'Location analytics', 'Virtual site visits'],
      buttonLink: '/services/land-bank'
    },
    {
      title: 'MoU Management',
      description: 'Digital MoU processing and lifecycle management.',
      icon: 'file-text',
      features: ['E-signing capability', 'Milestone tracking', 'Compliance monitoring'],
      buttonLink: '/services/mou'
    },
    {
      title: 'Clearance Status Tracker',
      description: 'Real-time tracking of all regulatory approvals.',
      icon: 'check-circle',
      features: ['Live updates', 'Department integration', 'Automated notifications'],
      buttonLink: '/services/clearance'
    },
    {
      title: 'Incentive Monitoring',
      description: 'Track and manage government incentives and subsidies.',
      icon: 'dollar-sign',
      features: ['Eligibility checker', 'Application tracking', 'Disbursement status'],
      buttonLink: '/services/incentives'
    },
    {
      title: 'Stakeholder Portal',
      description: 'Dedicated portals for different user types.',
      icon: 'users',
      features: ['Role-based access', 'Customized dashboards', 'Collaboration tools'],
      buttonLink: '/services/stakeholder'
    }
  ];

  investmentSteps: InvestmentStep[] = [
    {
      label: 'Application & Intake',
      description: 'Submit your investment proposal through our unified digital platform with all required documents.',
      features: ['Online CAF submission', 'Document upload', 'Application tracking']
    },
    {
      label: 'Proposal Review',
      description: 'Expert evaluation of your proposal with transparent criteria and defined timelines.',
      features: ['Technical evaluation', 'Financial assessment', 'Sector-specific review']
    },
    {
      label: 'Clearances & Documents',
      description: 'Streamlined processing of all regulatory clearances through integrated government systems.',
      features: ['Environmental clearance', 'Industry licenses', 'Tax registrations']
    },
    {
      label: 'Land Finalization',
      description: 'Land allocation, project monitoring, and ongoing support throughout implementation.',
      features: ['Land bank access', 'Project monitoring', 'Ongoing support']
    }
  ];

  sectors: Sector[] = [
    {
      title: 'Agriculture & Food Processing',
      description: 'Abundant natural resources and favorable climate for agri-business.',
      investment: '450',
      projects: '45+',
      opportunities: ['Organic farming', 'Food processing', 'Spice cultivation'],
      image: 'assets/images/agriculture.jpg',
      buttonLink: '/sectors/agriculture'
    },
    {
      title: 'Information Technology',
      description: 'Digital infrastructure and skilled workforce for IT services.',
      investment: '320',
      projects: '28+',
      opportunities: ['Software development', 'BPO services', 'Digital solutions'],
      image: 'assets/images/it.jpg',
      buttonLink: '/sectors/it'
    },
    {
      title: 'Textiles & Handicrafts',
      description: 'Traditional craftsmanship meets modern manufacturing.',
      investment: '280',
      projects: '35+',
      opportunities: ['Handloom', 'Silk production', 'Handicraft export'],
      image: 'assets/images/textiles.jpg',
      buttonLink: '/sectors/textiles'
    },
    {
      title: 'Tourism & Hospitality',
      description: 'Rich cultural heritage and natural beauty destinations.',
      investment: '220',
      projects: '20+',
      opportunities: ['Eco-tourism', 'Heritage hotels', 'Adventure tourism'],
      image: 'assets/images/tourism.jpg',
      buttonLink: '/sectors/tourism'
    },
    {
      title: 'Manufacturing',
      description: 'Strategic location with connectivity to Southeast Asia.',
      investment: '380',
      projects: '32+',
      opportunities: ['Light engineering', 'Electronics', 'Consumer goods'],
      image: 'assets/images/manufacturing.jpg',
      buttonLink: '/sectors/manufacturing'
    },
    {
      title: 'Logistics & Transportation',
      description: 'Gateway to Southeast Asia with developing infrastructure.',
      investment: '150',
      projects: '15+',
      opportunities: ['Warehousing', 'Cargo services', 'Freight forwarding'],
      image: 'assets/images/logistics.jpg',
      buttonLink: '/sectors/logistics'
    }
  ];

  dashboardStats: DashboardStat[] = [
    { label: 'Total Applications', value: '1,247', change: '+23% this quarter' },
    { label: 'MoUs Signed', value: '156', change: '+18% this month' },
    { label: 'Investment Value', value: '₹2,847 Cr', change: '+35% YTD' },
    { label: 'Active Projects', value: '89', change: '+12% this quarter' },
    { label: 'Avg. Processing Time', value: '28 Days', change: '-40% improvement' },
    { label: 'Success Rate', value: '94%', change: '+8% this year' }
  ];

  supportOptions: SupportOption[] = [
    {
      title: 'Live Chat',
      description: 'Instant support via our AI-powered chatbot, 24/7 available.',
      icon: 'chat',
      buttonText: 'Start Chat',
      buttonLink: '/support/chat'
    },
    {
      title: 'Phone Support',
      description: 'Speak with our investment facilitation team, Mon-Fri, 9 AM - 6 PM.',
      icon: 'phone',
      buttonText: 'Call Now',
      buttonLink: '/support/phone'
    },
    {
      title: 'Email Support',
      description: 'Send your queries and get detailed responses within 4 hours.',
      icon: 'email',
      buttonText: 'Send Email',
      buttonLink: '/support/email'
    },
    {
      title: 'Visit Office',
      description: 'Meet our team in person for detailed consultation, by appointment.',
      icon: 'place',
      buttonText: 'Book Visit',
      buttonLink: '/support/visit'
    }
  ];

  @HostListener('window:resize', ['$event'])
  onResize(event: Event) {
    this.isMobile = window.innerWidth <= 768;
  }

  ngOnInit(): void {
    this.startCarousel();
  }

  startCarousel(): void {
    setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide(): void {
    this.currentSlide = (this.currentSlide + 1) % this.carouselSlides.length;
  }

  prevSlide(): void {
    this.currentSlide = this.currentSlide === 0 ? this.carouselSlides.length - 1 : this.currentSlide - 1;
  }

  goToSlide(index: number): void {
    this.currentSlide = index;
  }

  toggleMobileMenu(): void {
    this.mobileMenuOpen = !this.mobileMenuOpen;
  }
}